import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import ForHer from "./pages/ForHer";
import ForHerBags from "./pages/ForHerBags";
import ForHerShoes from "./pages/ForHerShoes";
import ForHerJewelry from "./pages/ForHerJewelry";
import ForHerAccessories from "./pages/ForHerAccessories";
import ForHerFollower from "./pages/ForHerFollower";
import ForHim from "./pages/ForHim";
import ForHimBags from "./pages/ForHimBags";
import ForHimShoes from "./pages/ForHimShoes";
import ForHimJewelry from "./pages/ForHimJewelry";
import ForHimAccessories from "./pages/ForHimAccessories";
import ForHimFragrances from "./pages/ForHimFragrances";
import NotFound from "./pages/NotFound";
import ProductDetail from "./pages/ProductDetail";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/gifts/for-her" element={<ForHer />} />
          <Route path="/gifts/for-her/bags" element={<ForHerBags />} />
          <Route path="/gifts/for-her/shoes" element={<ForHerShoes />} />
          <Route path="/gifts/for-her/jewelry" element={<ForHerJewelry />} />
          <Route path="/gifts/for-her/accessories" element={<ForHerAccessories />} />
          <Route path="/gifts/for-her/follower" element={<ForHerFollower />} />
          <Route path="/gifts/for-her/:category/:productId" element={<ProductDetail />} />
          <Route path="/gifts/for-him" element={<ForHim />} />
          <Route path="/gifts/for-him/bags" element={<ForHimBags />} />
          <Route path="/gifts/for-him/shoes" element={<ForHimShoes />} />
          <Route path="/gifts/for-him/jewelry" element={<ForHimJewelry />} />
          <Route path="/gifts/for-him/accessories" element={<ForHimAccessories />} />
          <Route path="/gifts/for-him/fragrances" element={<ForHimFragrances />} />
          <Route path="/gifts/for-him/:category/:productId" element={<ProductDetail />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
