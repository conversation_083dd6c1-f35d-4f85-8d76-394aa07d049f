import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { ArrowLeft, Filter, Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import SharedNav from "@/components/SharedNav";
import { allProducts } from "../data/products";

const items = allProducts.follower;

const ForHerFollower = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const backgroundImage = "https://images.unsplash.com/photo-1464983953574-0892a716854b";
  const menuRef = useRef(null);
  const buttonRef = useRef(null);
  const [visibleCount, setVisibleCount] = useState(4);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filterFade, setFilterFade] = useState(false);
  const [filterFlowers, setFilterFlowers] = useState(true);
  const [filterPerfumes, setFilterPerfumes] = useState(true);
  const [priceRange, setPriceRange] = useState('all');
  const [sortBy, setSortBy] = useState("newest");
  const filterRef = useRef(null);
  const filterButtonRef = useRef(null);

  useEffect(() => {
    if (!menuOpen) return;
    function handleClickOutside(event) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  useEffect(() => {
    setVisibleCount(4);
  }, []);

  // Close filter popup on outside click
  useEffect(() => {
    if (!filterOpen) return;
    function handleClickOutside(event) {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target) &&
        filterButtonRef.current &&
        !filterButtonRef.current.contains(event.target)
      ) {
        setFilterFade(true);
        setTimeout(() => {
          setFilterOpen(false);
          setFilterFade(false);
        }, 200); // match the fade duration
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [filterOpen]);

  function parsePrice(price) {
    return Number(price.replace(/[^\d.]/g, ""));
  }

  let filteredItems = items.filter(
    (item) =>
      ((filterFlowers && item.type === "flower") ||
        (filterPerfumes && item.type === "perfume")) &&
      (
        priceRange === 'all' ? true :
        priceRange === '0-100' ? parsePrice(item.price) >= 0 && parsePrice(item.price) <= 100 :
        priceRange === '100-200' ? parsePrice(item.price) > 100 && parsePrice(item.price) <= 200 :
        priceRange === '200-300' ? parsePrice(item.price) > 200 && parsePrice(item.price) <= 300 :
        priceRange === '300+' ? parsePrice(item.price) > 300 : true
      )
  );

  if (sortBy === "oldest") {
    filteredItems = [...filteredItems].reverse();
  } else if (sortBy === "priceLow") {
    filteredItems = [...filteredItems].sort((a, b) => parsePrice(a.price) - parsePrice(b.price));
  } else if (sortBy === "priceHigh") {
    filteredItems = [...filteredItems].sort((a, b) => parsePrice(b.price) - parsePrice(a.price));
  }

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      {/* Content Container */}
      <div className="relative z-20">
        <SharedNav />
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="space-y-4">
                <Link
                  to="/gifts/for-her"
                  className="inline-flex items-center space-x-2 text-white/70 hover:text-white transition-colors text-sm font-light tracking-wide"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to For Her</span>
                </Link>
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Women's Collection
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Flowers & Perfumes
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Discover our curated selection of luxurious flowers and iconic perfumes—perfect for gifting or indulging yourself.
                </p>
              </div>
              <div className="flex justify-center pt-8 space-x-4 relative mb-8">
                <div className="relative">
                  <Button
                    ref={filterButtonRef}
                    variant="outline"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm flex items-center space-x-2"
                    onClick={() => {
                      if (filterOpen) {
                        setFilterFade(true);
                        setTimeout(() => {
                          setFilterOpen(false);
                          setFilterFade(false);
                        }, 200); // match the fade duration
                      } else {
                        setFilterOpen(true);
                      }
                    }}
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filters</span>
                  </Button>
                  {filterOpen && (
                    <div
                      ref={filterRef}
                      className={`absolute left-0 top-full mt-2 w-64 rounded-xl shadow-lg z-50 bg-white/10 backdrop-blur-lg border border-white/20 p-4 transition-opacity duration-200 ${filterFade ? 'opacity-0' : 'opacity-100'}`}
                      style={{
                        boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
                        background: "rgba(255,255,255,0.15)",
                        border: "1px solid rgba(255,255,255,0.18)",
                      }}
                    >
                      <h2 className="text-lg font-semibold text-white mb-6">Filter Products</h2>
                      <div className="space-y-4">
                        <label className="flex items-center space-x-2 text-white">
                          <input
                            type="checkbox"
                            checked={filterFlowers}
                            onChange={() => setFilterFlowers((v) => !v)}
                            className="accent-white h-4 w-4 rounded"
                          />
                          <span>Flowers</span>
                        </label>
                        <label className="flex items-center space-x-2 text-white">
                          <input
                            type="checkbox"
                            checked={filterPerfumes}
                            onChange={() => setFilterPerfumes((v) => !v)}
                            className="accent-white h-4 w-4 rounded"
                          />
                          <span>Perfumes</span>
                        </label>
                        <div className="text-white space-y-1">
                          <div className="mb-1">Price Range:</div>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="all" checked={priceRange === 'all'} onChange={() => setPriceRange('all')} className="accent-white" />
                            <span>All</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="0-100" checked={priceRange === '0-100'} onChange={() => setPriceRange('0-100')} className="accent-white" />
                            <span>$0–$100</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="100-200" checked={priceRange === '100-200'} onChange={() => setPriceRange('100-200')} className="accent-white" />
                            <span>$100–$200</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="200-300" checked={priceRange === '200-300'} onChange={() => setPriceRange('200-300')} className="accent-white" />
                            <span>$200–$300</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="300+" checked={priceRange === '300+'} onChange={() => setPriceRange('300+')} className="accent-white" />
                            <span>$300+</span>
                          </label>
                        </div>
                        <div className="text-white">
                          <label className="block mb-1">Sort by:</label>
                          <select
                            value={sortBy}
                            onChange={e => setSortBy(e.target.value)}
                            className="w-full px-2 py-1 rounded bg-white/20 border border-white/30 text-white"
                          >
                            <option value="newest">Newest to Oldest</option>
                            <option value="oldest">Oldest to Newest</option>
                            <option value="priceLow">Price: Low to High</option>
                            <option value="priceHigh">Price: High to Low</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="relative">
                  <Button
                    ref={buttonRef}
                    variant="outline"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm flex items-center space-x-2"
                    onClick={() => setMenuOpen((open) => !open)}
                  >
                    <Menu className="h-4 w-4" />
                    <span>Flowers & Perfumes</span>
                  </Button>
                  {menuOpen && (
                    <div
                      ref={menuRef}
                      className="absolute right-0 mt-2 w-48 rounded-xl shadow-lg z-50 bg-white/10 backdrop-blur-lg border border-white/20 p-4"
                      style={{
                        boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
                        background: "rgba(255,255,255,0.15)",
                        border: "1px solid rgba(255,255,255,0.18)",
                      }}
                    >
                      <ul className="space-y-2 text-left">
                        <li>
                          <Link to="/gifts/for-her/bags" className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>Bags</Link>
                        </li>
                        <li>
                          <Link to="/gifts/for-her/shoes" className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>Shoes</Link>
                        </li>
                        <li>
                          <Link to="/gifts/for-her/jewelry" className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>Jewelry</Link>
                        </li>
                        <li>
                          <Link to="/gifts/for-her/accessories" className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>Accessories</Link>
                        </li>
                        <li>
                          <Link to="/gifts/for-her/follower" className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>Flowers & Perfumes</Link>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {/* Flowers & Perfumes Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredItems.slice(0, visibleCount).map((item) => (
                <div key={item.id} className="group cursor-pointer" onClick={() => window.location.href = `/gifts/for-her/follower/${item.id}` }>
                  {/* Product Image */}
                  <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-4 relative border border-white/20">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                  </div>
                  {/* Product Info */}
                  <div className="text-center">
                    <h3 className="font-light text-sm mb-1 text-white/90 group-hover:text-white transition-colors">
                      {item.name}
                    </h3>
                    <p className="text-sm text-white/70 font-light">{item.price}</p>
                  </div>
                </div>
              ))}
            </div>
            {visibleCount < filteredItems.length && (
              <div className="text-center mt-16">
                <Button
                  variant="outline"
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-8 py-3 font-light tracking-wide"
                  onClick={() => setVisibleCount(filteredItems.length)}
                >
                  Load More Products
                </Button>
              </div>
            )}
          </div>
        </section>
        <footer className="px-8 py-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-8">
              <div className="font-serif text-2xl font-light tracking-wider text-white">
                LORIYA
              </div>
              <div className="flex justify-center space-x-8 text-sm font-light text-white/70">
                <Link to="/" className="hover:text-white transition-colors">Home</Link>
                <a href="#" className="hover:text-white transition-colors">Collections</a>
                <a href="#" className="hover:text-white transition-colors">About</a>
                <a href="#" className="hover:text-white transition-colors">Contact</a>
              </div>
              <div className="pt-8 border-t border-white/20">
                <p className="text-xs font-light text-white/50">
                  © 2024 Loriya. All rights reserved.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default ForHerFollower; 