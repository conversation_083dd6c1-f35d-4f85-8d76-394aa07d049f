import { <PERSON> } from "react-router-dom";
import SharedNav from "@/components/SharedNav";
import { Button } from "@/components/ui/button";
import { useState, useRef, useEffect } from "react";
import { Menu, Filter } from "lucide-react";

const categories = [
  {
    name: "Bags",
    path: "/gifts/for-him/bags",
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop",
    preview: [
      { name: "Keepall 50", price: "$2,390.00", image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop" },
      { name: "District PM", price: "$2,190.00", image: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop" },
    ],
  },
  {
    name: "Shoes",
    path: "/gifts/for-him/shoes",
    image: "https://images.unsplash.com/photo-1600269452121-4f2416e55c28?q=80&w=2065&auto=format&fit=crop",
    preview: [
      { name: "Run Away Sneaker", price: "$1,150.00", image: "https://images.unsplash.com/photo-1600269452121-4f2416e55c28?q=80&w=2065&auto=format&fit=crop" },
      { name: "Luxembourg Sneaker", price: "$1,250.00", image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=2012&auto=format&fit=crop" },
    ],
  },
  {
    name: "Jewelry",
    path: "/gifts/for-him/jewelry",
    image: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2070&auto=format&fit=crop",
    preview: [
      { name: "LV Iconic Cufflinks", price: "$655.00", image: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=2070&auto=format&fit=crop" },
      { name: "LV Iconic Ring", price: "$525.00", image: "https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=2070&auto=format&fit=crop" },
    ],
  },
  {
    name: "Accessories",
    path: "/gifts/for-him/accessories",
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop",
    preview: [
      { name: "LV Iconic Belt", price: "$695.00", image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop" },
      { name: "Monogram Tie", price: "$285.00", image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=2187&auto=format&fit=crop" },
    ],
  },
  {
    name: "Fragrances",
    path: "/gifts/for-him/fragrances",
    image: "https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=400&q=80",
    preview: [
      { name: "Bleu de Chanel", price: "$180.00", image: "https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=400&q=80" },
      { name: "Dior Sauvage", price: "$160.00", image: "https://images.unsplash.com/photo-1519681393784-d120267933ba?auto=format&fit=crop&w=400&q=80" },
    ],
  },
];

const ForHim = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filterFade, setFilterFade] = useState(false);
  const [menuFade, setMenuFade] = useState(false);
  const filterRef = useRef(null);
  const filterButtonRef = useRef(null);
  const menuRef = useRef(null);
  const menuButtonRef = useRef(null);

  // Close filter popup on outside click
  useEffect(() => {
    if (!filterOpen) return;
    function handleClickOutside(event) {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target) &&
        filterButtonRef.current &&
        !filterButtonRef.current.contains(event.target)
      ) {
        setFilterFade(true);
        setTimeout(() => {
          setFilterOpen(false);
          setFilterFade(false);
        }, 200);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [filterOpen]);

  // Close menu popup on outside click
  useEffect(() => {
    if (!menuOpen) return;
    function handleClickOutside(event) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        menuButtonRef.current &&
        !menuButtonRef.current.contains(event.target)
      ) {
        setMenuFade(true);
        setTimeout(() => {
          setMenuOpen(false);
          setMenuFade(false);
        }, 200);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div className="fixed inset-0 bg-cover bg-center bg-no-repeat" style={{ backgroundImage: `url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=1500&q=80')` }} />
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      {/* Content Container */}
      <div className="relative z-20">
        <SharedNav />
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <p className="text-sm font-light tracking-widest text-white/80 uppercase">Men's Collection</p>
              <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white mb-4">For Him</h1>
              <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">Discover our curated selection of luxury gifts for men. Explore bags, shoes, jewelry, accessories, and fragrances—perfect for every occasion.</p>
            </div>
            <div className="flex justify-center pt-8 space-x-4 mb-8">
              <div className="relative">
                <Button
                  ref={menuButtonRef}
                  variant="outline"
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm flex items-center space-x-2"
                  onClick={() => {
                    if (menuOpen) {
                      setMenuFade(true);
                      setTimeout(() => {
                        setMenuOpen(false);
                        setMenuFade(false);
                      }, 200);
                    } else {
                      setMenuOpen(true);
                    }
                  }}
                >
                  <Menu className="h-4 w-4" />
                  <span>Categories</span>
                </Button>
                {menuOpen && (
                  <div
                    ref={menuRef}
                    className={`absolute right-0 mt-2 w-48 rounded-xl shadow-lg z-50 bg-black/40 backdrop-blur-lg border border-white/30 p-4 transition-opacity duration-200 ${menuFade ? 'opacity-0' : 'opacity-100'}`}
                    style={{
                      boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
                      background: "rgba(20,20,30,0.40)",
                      border: "1px solid rgba(255,255,255,0.22)",
                    }}
                  >
                    <ul className="space-y-2 text-left">
                      {categories.map((cat) => (
                        <li key={cat.name}>
                          <Link to={cat.path} className="block px-2 py-1 rounded hover:bg-white/20 text-white/90 transition-colors" onClick={() => setMenuOpen(false)}>{cat.name}</Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {categories.map((cat) => (
                <div key={cat.name} className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 p-6 flex flex-col items-center">
                  <img src={cat.image} alt={cat.name} className="w-32 h-32 object-cover rounded-lg mb-4 border border-white/30" />
                  <h2 className="text-2xl font-serif text-white mb-2">{cat.name}</h2>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {cat.preview.map((item) => (
                      <div key={item.name} className="bg-white/10 rounded-lg p-2 flex flex-col items-center border border-white/10">
                        <img src={item.image} alt={item.name} className="w-16 h-16 object-cover rounded mb-1" />
                        <span className="text-xs text-white/80">{item.name}</span>
                        <span className="text-xs text-white/60">{item.price}</span>
                      </div>
                    ))}
                  </div>
                  <Link to={cat.path}>
                    <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-6 py-2 font-light tracking-wide">See All</Button>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ForHim;
