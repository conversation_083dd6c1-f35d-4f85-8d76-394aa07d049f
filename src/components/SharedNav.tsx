import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>u, Heart, User, ShoppingBag, ArrowLeft, X, Plus, Minus, Trash2 } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";

// Mock cart data - in a real app this would come from state management
const mockCartItems = [
  {
    id: 1,
    name: "Elegant Pearl Necklace",
    price: 299,
    quantity: 1,
    image: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=400&fit=crop&crop=center",
    size: "One Size",
    color: "Pearl White"
  },
  {
    id: 2,
    name: "Designer Leather Handbag",
    price: 599,
    quantity: 2,
    image: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=400&fit=crop&crop=center",
    size: "Medium",
    color: "Black"
  }
];

const SharedNav = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentMenu, setCurrentMenu] = useState("main");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [cartItems, setCartItems] = useState(mockCartItems);

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCartItems(cartItems.filter(item => item.id !== id));
    } else {
      setCartItems(cartItems.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ));
    }
  };

  const removeItem = (id: number) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  return (
    <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
      <div className="grid grid-cols-3 items-center">
        <div className="flex justify-start">
          <Sheet open={isMenuOpen} onOpenChange={(open) => {
            setIsMenuOpen(open);
            if (open) {
              setCurrentMenu("main"); // Reset to main menu when opening
            }
          }}>
            <SheetTrigger asChild>
              <button
                className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"
              >
                <Menu size={20} />
                <span className="text-sm font-light tracking-wide">MENU</span>
              </button>
            </SheetTrigger>
            <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-none text-white p-0 sm:max-w-sm">
              <div className="h-full flex flex-col p-8 pt-20">
                {currentMenu === "main" && (
                  <>
                    <div className="flex-grow flex flex-col space-y-6">
                      <div
                        className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer"
                        onClick={() => setCurrentMenu("gifts")}
                      >
                        <span className="text-lg font-light">Gifts</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">New</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Bags and Wallets</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Women</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Men</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Jewelry</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Watches</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Perfumes</span>
                        <span>›</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 mt-8">
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                        <div className="mt-4 space-y-2">
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">ADDRESS</span>
                            <span>→</span>
                          </div>
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">OPENING HOURS</span>
                            <span>→</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {currentMenu === "gifts" && (
                  <>
                    <div className="flex items-center gap-3 mb-8">
                      <button
                        onClick={() => setCurrentMenu("main")}
                        className="text-white/80 hover:text-white transition-colors"
                      >
                        <ArrowLeft size={20} />
                      </button>
                      <span className="text-white/80 font-light">BACK</span>
                    </div>

                    <div className="mb-8">
                      <h2 className="text-2xl font-light text-white">Gifts</h2>
                    </div>

                    <div className="flex-grow flex flex-col space-y-6">
                      <Link
                        to="/gifts/for-her"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Her
                      </Link>
                      <Link
                        to="/gifts/for-home"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Home
                      </Link>
                      <Link
                        to="/gifts/for-babies"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Babies
                      </Link>
                      <Link
                        to="/gifts/personalization"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Personalization
                      </Link>
                    </div>

                    <div className="flex-shrink-0 mt-8">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
        <div className="text-center font-serif text-2xl font-light tracking-wider text-white">
          <Link to="/" className="hover:text-gray-200 transition-colors">
            LORIYA
          </Link>
        </div>
        <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
          <button className="text-sm font-light tracking-wide hover:text-gray-200 transition-colors hidden md:block">
            Contact Us
          </button>
          <button className="hover:text-gray-200 transition-colors">
            <Heart size={20} />
          </button>
          <button className="hover:text-gray-200 transition-colors">
            <User size={20} />
          </button>
          <button
            className="relative hover:text-gray-200 transition-colors"
            onClick={() => setIsCartOpen(true)}
          >
            <ShoppingBag size={20} />
            {getTotalItems() > 0 && (
              <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                {getTotalItems()}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Glassy Cart Popup */}
      {isCartOpen && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsCartOpen(false)}
          />

          {/* Cart Modal */}
          <div
            className="relative w-full max-w-md bg-black/40 backdrop-blur-lg border border-white/30 rounded-xl shadow-2xl max-h-[80vh] flex flex-col"
            style={{
              boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
              background: "rgba(20,20,30,0.40)",
              border: "1px solid rgba(255,255,255,0.22)",
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-xl font-light text-white">Shopping Cart</h2>
              <button
                onClick={() => setIsCartOpen(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-6">
              {cartItems.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingBag size={48} className="mx-auto text-white/30 mb-4" />
                  <p className="text-white/70 font-light">Your cart is empty</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex gap-4 p-4 bg-white/5 rounded-lg border border-white/10">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-light text-sm truncate">{item.name}</h3>
                        <p className="text-white/60 text-xs mt-1">{item.color} • {item.size}</p>
                        <p className="text-white font-medium text-sm mt-1">${item.price}</p>

                        {/* Quantity Controls */}
                        <div className="flex items-center gap-2 mt-2">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="w-6 h-6 rounded-full bg-white/10 border border-white/20 flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-colors"
                          >
                            <Minus size={12} />
                          </button>
                          <span className="text-white text-sm w-8 text-center">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="w-6 h-6 rounded-full bg-white/10 border border-white/20 flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 transition-colors"
                          >
                            <Plus size={12} />
                          </button>
                        </div>
                      </div>
                      <button
                        onClick={() => removeItem(item.id)}
                        className="text-white/50 hover:text-red-400 transition-colors self-start"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {cartItems.length > 0 && (
              <div className="p-6 border-t border-white/20">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-white/80 font-light">Total:</span>
                  <span className="text-white text-lg font-medium">${getTotalPrice()}</span>
                </div>
                <div className="space-y-2">
                  <Button
                    className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm font-light tracking-wide"
                    variant="outline"
                  >
                    View Cart
                  </Button>
                  <Button
                    className="w-full bg-white text-black hover:bg-white/90 font-light tracking-wide"
                  >
                    Checkout
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default SharedNav; 