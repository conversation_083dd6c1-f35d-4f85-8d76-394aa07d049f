import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>u, Heart, User, ShoppingBag, ArrowLeft, X, Plus, Minus, Trash2 } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";

// Mock cart data - in a real app this would come from state management
const mockCartItems = [
  {
    id: 1,
    name: "Elegant Pearl Necklace",
    price: 299,
    quantity: 1,
    image: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=400&fit=crop&crop=center",
    size: "One Size",
    color: "Pearl White"
  },
  {
    id: 2,
    name: "Designer Leather Handbag",
    price: 599,
    quantity: 2,
    image: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=400&fit=crop&crop=center",
    size: "Medium",
    color: "Black"
  }
];

const SharedNav = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentMenu, setCurrentMenu] = useState("main");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [cartItems, setCartItems] = useState(mockCartItems);

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCartItems(cartItems.filter(item => item.id !== id));
    } else {
      setCartItems(cartItems.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ));
    }
  };

  const removeItem = (id: number) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  return (
    <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
      <div className="grid grid-cols-3 items-center">
        <div className="flex justify-start">
          <Sheet open={isMenuOpen} onOpenChange={(open) => {
            setIsMenuOpen(open);
            if (open) {
              setCurrentMenu("main"); // Reset to main menu when opening
            }
          }}>
            <SheetTrigger asChild>
              <button
                className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"
              >
                <Menu size={20} />
                <span className="text-sm font-light tracking-wide">MENU</span>
              </button>
            </SheetTrigger>
            <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-none text-white p-0 sm:max-w-sm">
              <div className="h-full flex flex-col p-8 pt-20">
                {currentMenu === "main" && (
                  <>
                    <div className="flex-grow flex flex-col space-y-6">
                      <div
                        className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer"
                        onClick={() => setCurrentMenu("gifts")}
                      >
                        <span className="text-lg font-light">Gifts</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">New</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Bags and Wallets</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Women</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Men</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Jewelry</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Watches</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Perfumes</span>
                        <span>›</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 mt-8">
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                        <div className="mt-4 space-y-2">
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">ADDRESS</span>
                            <span>→</span>
                          </div>
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">OPENING HOURS</span>
                            <span>→</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {currentMenu === "gifts" && (
                  <>
                    <div className="flex items-center gap-3 mb-8">
                      <button
                        onClick={() => setCurrentMenu("main")}
                        className="text-white/80 hover:text-white transition-colors"
                      >
                        <ArrowLeft size={20} />
                      </button>
                      <span className="text-white/80 font-light">BACK</span>
                    </div>

                    <div className="mb-8">
                      <h2 className="text-2xl font-light text-white">Gifts</h2>
                    </div>

                    <div className="flex-grow flex flex-col space-y-6">
                      <Link
                        to="/gifts/for-her"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Her
                      </Link>
                      <Link
                        to="/gifts/for-home"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Home
                      </Link>
                      <Link
                        to="/gifts/for-babies"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Babies
                      </Link>
                      <Link
                        to="/gifts/personalization"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Personalization
                      </Link>
                    </div>

                    <div className="flex-shrink-0 mt-8">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
        <div className="text-center font-serif text-2xl font-light tracking-wider text-white">
          <Link to="/" className="hover:text-gray-200 transition-colors">
            LORIYA
          </Link>
        </div>
        <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
          <button className="text-sm font-light tracking-wide hover:text-gray-200 transition-colors hidden md:block">
            Contact Us
          </button>
          <button className="hover:text-gray-200 transition-colors">
            <Heart size={20} />
          </button>
          <button className="hover:text-gray-200 transition-colors">
            <User size={20} />
          </button>
          <button
            className="relative hover:text-gray-200 transition-colors"
            onClick={() => setIsCartOpen(true)}
          >
            <ShoppingBag size={20} />
            {getTotalItems() > 0 && (
              <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
                {getTotalItems()}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Glassy Cart Popup */}
      {isCartOpen && (
        <div className="fixed inset-0 z-[100]">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsCartOpen(false)}
          />

          {/* Cart Modal - Positioned on the right side with elegant darker glassy styling */}
          <div
            className="absolute top-0 right-0 w-full sm:w-80 lg:w-96 h-full backdrop-blur-xl border-l shadow-2xl flex flex-col"
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.05)",
              background: "rgba(15, 15, 20, 0.85)",
              borderLeft: "1px solid rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(20px) saturate(180%)",
            }}
          >
            {/* Header */}
            <div className="p-8 pb-6">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-2xl font-light text-white/95 tracking-wide">Shopping Cart</h2>
                <button
                  onClick={() => setIsCartOpen(false)}
                  className="flex items-center gap-2 text-white/60 hover:text-white/90 transition-all duration-200 bg-white/5 hover:bg-white/10 px-4 py-2 rounded-full text-sm border border-white/10 hover:border-white/20"
                >
                  <X size={16} />
                  <span className="font-light">Close</span>
                </button>
              </div>
              <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto px-8 pb-4">
              {cartItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center py-16">
                  <div className="w-20 h-20 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl flex items-center justify-center mb-6 border border-white/10">
                    <ShoppingBag size={28} className="text-white/60" />
                  </div>
                  <p className="text-white/80 text-xl font-light mb-3">Your cart is empty</p>
                  <p className="text-white/50 text-sm font-light">Discover our collection and add items to your cart</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {cartItems.map((item) => (
                    <div key={item.id} className="group">
                      <div
                        className="bg-gradient-to-br from-white/8 to-white/3 rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-gradient-to-br hover:from-white/12 hover:to-white/5"
                        style={{
                          backdropFilter: "blur(10px)",
                        }}
                      >
                        <div className="flex flex-col gap-4">
                          {/* Product Image and Basic Info */}
                          <div className="flex gap-4">
                            <div className="relative">
                              <img
                                src={item.image}
                                alt={item.name}
                                className="w-20 h-20 object-cover rounded-xl border border-white/10"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="text-white/95 font-medium text-base mb-1 truncate">{item.name}</h3>
                              <p className="text-white/50 text-sm font-light mb-2">{item.color} • {item.size}</p>
                              <span className="text-white/95 font-semibold text-lg">${item.price}</span>
                            </div>
                            <button
                              onClick={() => removeItem(item.id)}
                              className="text-white/40 hover:text-red-400 transition-all duration-200 p-2 hover:bg-red-500/10 rounded-xl self-start"
                            >
                              <Trash2 size={18} />
                            </button>
                          </div>

                          {/* Quantity Controls - Full Width */}
                          <div className="flex items-center justify-center gap-4 pt-2">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white/90 transition-all duration-200 border border-white/10"
                            >
                              <Minus size={16} />
                            </button>
                            <div className="flex items-center gap-2">
                              <span className="text-white/60 text-sm font-light">Quantity:</span>
                              <span className="text-white/90 text-lg font-medium w-8 text-center">{item.quantity}</span>
                            </div>
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white/90 transition-all duration-200 border border-white/10"
                            >
                              <Plus size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer - Always show help section */}
            <div className="mt-auto">
              {cartItems.length > 0 && (
                <div className="px-8 py-6">
                  <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-6"></div>

                  {/* Total Section */}
                  <div className="bg-gradient-to-br from-white/8 to-white/3 rounded-2xl p-6 mb-6 border border-white/10">
                    <div className="flex justify-between items-center mb-6">
                      <span className="text-white/80 font-light text-lg">Total:</span>
                      <span className="text-white text-2xl font-semibold">${getTotalPrice()}</span>
                    </div>

                    <div className="space-y-4">
                      <Button
                        className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm font-light tracking-wide py-4 text-base rounded-xl transition-all duration-200 hover:border-white/30"
                        variant="outline"
                      >
                        View Full Cart
                      </Button>
                      <Button
                        className="w-full bg-gradient-to-r from-white to-white/95 text-black hover:from-white/90 hover:to-white/85 font-medium tracking-wide py-4 text-base rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
                      >
                        Proceed to Checkout
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Help Section - Always visible */}
              <div className="px-8 py-6 border-t border-white/10">
                <div className="text-center">
                  <p className="text-white/60 font-light text-sm mb-2">Need assistance?</p>
                  <p className="text-white/90 font-medium text-base tracking-wide">+1.866.LORIYA</p>
                  <p className="text-white/50 font-light text-xs mt-2">Available 24/7 for support</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default SharedNav; 